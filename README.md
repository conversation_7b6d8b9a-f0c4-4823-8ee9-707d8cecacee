<h1 align="center"><strong>⚽🏀🏐 SMP Online</strong></h1>

<p align="center">
Online reservation system for Sportpark Marie Pampoen
</p>

<p align="center">
<a href="https://github.com/PoloStyle07/smp_online/actions/workflows/tests.yml"><img src="https://github.com/PoloStyle07/smp_online/actions/workflows/tests.yml/badge.svg" alt="Test Status"></a>
<a href="https://github.com/PoloStyle07/smp_online/actions/workflows/lint.yml"><img src="https://github.com/PoloStyle07/smp_online/actions/workflows/lint.yml/badge.svg" alt="Linter Status"></a>
<a href="https://github.com/PoloStyle07/smp_online/actions/workflows/larastan.yml"><img src="https://github.com/PoloStyle07/smp_online/actions/workflows/larastan.yml/badge.svg" alt="Larastan Status"></a>
</p>

## General info

* Web App Framework: Laravel 12
* Database: SQLite for local development, Postgres for staging, MySQL for production
* Frontend Framework: Vite, Bootstrap 5
* Frontend Template: Ynex (modified), HTML based
* Authorization: Laravel RBAC (with customizations)
* Testing: PHPUnit
* Linting: Laravel Pint
* Static Analysis: Larastan
* CI/CD: GitHub Actions

## Dependencies and initial project setup

* Install Laravel Herd
  * Ensure PHP 8.4 is installed
  * Ensure NodeJS is installed
    * Check with `node -v`
  * Ensure NPM is installed
    * Check with `npm -v`
  * Windows only: Ensure NGINX is installed and running
* Run the following commands from the root `smp_online` folder (Windows: `powershell`, Mac: `zsh`)
    * `composer install`
      * Installs composer dependencies (in `vendor` folder)
    * `npm install`
      * Installs npm dependencies (in `node_modules` folder)
    * `npm run build`
      * Builds the frontend assets (CSS, JS, etc.) config visible in vite.config.js
    * `cp .env.example .env`
      * Creates a new `.env` file from the example file
    * `php artisan key:generate`
      * Generates a new application key
    * `php artisan migrate` (Answer yes to create database if needed)
      * Creates the database tables (if not exists)
    * `php artisan migrate:fresh --seed`
      * Recreates the database tables (removes all existing data) and seeds the database with fresh test data

At this point you can go to `sites` in Laravel Herd and add a new site by linking it to the smp_online folder. You can then open the web app directly from Laravel Herd.

Alternatively if not using Laravel Herd, you can run `composer run dev` and access the site at `http://localhost:8000`.

### Windows troubleshooting

* If `npm` doesn't have permission to run scripts, try:
  * `Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted`
  * This sets the execution policy for the current user to unrestricted, which allows scripts to run without prompting for permission.

## Deploying to staging

The staging environment provides a production-like testing environment for validating changes before they reach production. The staging environment runs on Laravel Cloud and uses PostgreSQL as its database.

### Deployment Triggers

Deployment to the staging environment is automatically triggered when:

1. **Direct commits to staging branch**: Committing changes directly to the `staging` branch
2. **Merging into staging branch**: Merging pull requests or branches into the `staging` branch

### Laravel Cloud Access

The staging environment runs on **Laravel Cloud**. To access the staging environment:

- **Hybernation**: Staging runs on a hybernation server,  please take into account the first open will be slow and take a few seconds.
- **Environment URL**:
  - https://smp-online-staging-7ebvmc.laravel.cloud/
- **Dashboard access**: Laravel Cloud dashboard access is required for monitoring deployments and logs. Contact **PoloStyle** to request access to the Laravel Cloud staging environment

### Best Practices

- **Test locally first**: Always test changes thoroughly in your local environment
- **Use feature branches**: Develop features in separate branches before merging to staging
- **Monitor after deployment**: Check the staging environment immediately after deployment
- **Document breaking changes**: Clearly document any changes that might affect other developers
- **Coordinate with team**: Communicate with team members before deploying significant changes

### Emergency Rollback

If critical issues are discovered after deployment:

1. **Identify the last known good commit** on the staging branch
2. **Reset staging branch** to the previous commit:
   ```bash
   git checkout staging
   git reset --hard <last-good-commit-hash>
   git push --force-with-lease origin staging
   ```
3. **Notify the team** about the rollback and the issues encountered
4. **Fix the issues** in a separate branch before attempting to redeploy

## Contributing

Thank you for considering contributing to SMP Online! The contribution guide can be found in the [contributing guide](CONTRIBUTING.md).

## License

SMP Online is closed-sourced software licensed under our [proprietary license](LICENSE.md).
