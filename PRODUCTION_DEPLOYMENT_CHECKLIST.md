# Production Deployment Checklist for Cookie Domain Issues

## 1. Environment Configuration (.env file)

### Required Changes for Production:
```bash
# Application Settings
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Database Configuration (PostgreSQL)
DB_CONNECTION=pgsql
DB_HOST=your-postgres-host
DB_PORT=5432
DB_DATABASE=your_database_name
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Session Configuration - CRITICAL FOR COOKIE ISSUES
SESSION_DRIVER=database
SESSION_DOMAIN=.yourdomain.com  # Use .yourdomain.com for subdomains or yourdomain.com for exact domain
SESSION_SECURE_COOKIE=true      # MUST be true for HTTPS
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=lax
SESSION_PARTITIONED_COOKIE=false
```

## 2. Database Setup

### Run these commands on production:
```bash
# Run migrations (including sessions table)
php artisan migrate

# Clear and cache configuration
php artisan config:clear
php artisan config:cache

# Clear and cache routes
php artisan route:clear
php artisan route:cache

# Clear and cache views
php artisan view:clear
php artisan view:cache
```

## 3. Session Domain Configuration Rules

### Choose the correct SESSION_DOMAIN value:

1. **For exact domain matching:**
   - `SESSION_DOMAIN=yourdomain.com`
   - Cookies will ONLY work on yourdomain.com

2. **For subdomain support:**
   - `SESSION_DOMAIN=.yourdomain.com`
   - Cookies will work on yourdomain.com, www.yourdomain.com, api.yourdomain.com, etc.

3. **For localhost/development:**
   - `SESSION_DOMAIN=null` or leave empty

## 4. HTTPS Requirements

### For production with HTTPS (required for secure cookies):
```bash
SESSION_SECURE_COOKIE=true
```

### This ensures cookies are only sent over HTTPS connections.

## 5. Troubleshooting Steps

### Step 1: Test Session Configuration
Visit: `https://yourdomain.com/debug/session-info`
Check the response for correct configuration values.

### Step 2: Test CSRF Token
Make a POST request to: `https://yourdomain.com/debug/test-csrf`
Include CSRF token in headers.

### Step 3: Test Session Storage
Visit: `https://yourdomain.com/debug/test-session`
Verify session read/write operations work.

### Step 4: Check Browser Developer Tools
1. Open Network tab
2. Make the failing AJAX request
3. Check Response Headers for Set-Cookie
4. Check if cookies are being set with correct domain

## 6. Common Issues and Solutions

### Issue: "Cookie has been rejected for invalid domain"
**Solutions:**
1. Ensure SESSION_DOMAIN matches your actual domain
2. Remove leading 'www.' from SESSION_DOMAIN if using subdomain format
3. Verify APP_URL matches your actual production URL

### Issue: CSRF token mismatch
**Solutions:**
1. Clear browser cookies and cache
2. Ensure SESSION_SECURE_COOKIE=true for HTTPS
3. Check that CSRF meta tag is present in HTML head

### Issue: Sessions not persisting
**Solutions:**
1. Verify sessions table exists in PostgreSQL
2. Check database connection is working
3. Ensure proper file permissions for storage directory

## 7. Laravel Cloud Specific Settings

### If deploying to Laravel Cloud:
1. Use the provided database credentials
2. Set SESSION_DOMAIN to your Laravel Cloud domain
3. Ensure HTTPS is enabled
4. Use environment variables for sensitive data

## 8. Final Verification

### After deployment, test:
1. User login/logout functionality
2. AJAX requests (like loadAvailability function)
3. CSRF protection on forms
4. Session persistence across page reloads

### Remove debug routes in production:
Comment out or remove the debug routes inclusion in `routes/web.php` for security.
