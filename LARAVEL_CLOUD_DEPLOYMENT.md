# Laravel Cloud Deployment Guide for Cookie Domain Issues

## Immediate Fix for Staging Environment

### 1. Update Environment Variables in Laravel Cloud Dashboard

Set these environment variables in your Laravel Cloud staging environment:

```bash
# Application Configuration
APP_URL=https://smp-online-staging-7ebvmc.laravel.cloud
APP_ENV=production
APP_DEBUG=false

# Critical Session Configuration for Cookie Issues
SESSION_DOMAIN=.laravel.cloud
SESSION_SECURE_COOKIE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=lax
SESSION_PARTITIONED_COOKIE=false

# Database (should already be configured by Laravel Cloud)
DB_CONNECTION=pgsql
```

### 2. Deploy Updated CORS Configuration

The updated `config/cors.php` now includes:
- Your specific staging domain: `https://smp-online-staging-7ebvmc.laravel.cloud`
- Pattern matching for all Laravel Cloud domains: `/^https:\/\/.*\.laravel\.cloud$/`
- Proper headers for AJAX requests including `X-CSRF-TOKEN`

### 3. Run Database Migrations

After deployment, run these commands in Laravel Cloud:

```bash
php artisan migrate
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## Testing the Fix

### 1. Test Session Configuration
Visit: `https://smp-online-staging-7ebvmc.laravel.cloud/debug/session-info`

Expected response should show:
```json
{
  "session_config": {
    "domain": ".laravel.cloud",
    "secure": true,
    "http_only": true,
    "same_site": "lax"
  },
  "app_config": {
    "app_url": "https://smp-online-staging-7ebvmc.laravel.cloud"
  },
  "request_info": {
    "is_secure": true
  }
}
```

### 2. Test AJAX Endpoints
1. Navigate to the reservation creation page
2. Open browser developer tools (Network tab)
3. Fill in field, date, and duration to trigger `loadAvailability()`
4. Check that the POST request to `/reservations/check-availability` succeeds
5. Verify cookies are being set with correct domain

### 3. Browser Developer Tools Verification
1. Open Application/Storage tab in DevTools
2. Check Cookies section
3. Verify `laravel_session` cookie has:
   - Domain: `.laravel.cloud`
   - Secure: ✓
   - HttpOnly: ✓
   - SameSite: `Lax`

## Common Issues and Solutions

### Issue: Still getting "Cookie rejected for invalid domain"
**Solution:** 
1. Clear all browser cookies for the domain
2. Verify `SESSION_DOMAIN=.laravel.cloud` (with leading dot)
3. Ensure `SESSION_SECURE_COOKIE=true`

### Issue: CSRF token mismatch
**Solution:**
1. Check that CSRF meta tag is in the HTML head
2. Verify `X-CSRF-TOKEN` header is being sent in AJAX requests
3. Clear browser cache and cookies

### Issue: 500 errors on AJAX requests
**Solution:**
1. Check Laravel logs in the dashboard
2. Verify database connection is working
3. Ensure sessions table exists (run migrations)

## Production Deployment (When Ready)

For your final production domain, update:

```bash
# Replace with your actual production domain
APP_URL=https://yourdomain.com
SESSION_DOMAIN=.yourdomain.com  # or yourdomain.com for exact match

# Add to config/cors.php allowed_origins:
'https://yourdomain.com'
```

## Security Notes

1. **Remove debug routes** in production by commenting out the include in `routes/web.php`
2. **Set APP_DEBUG=false** in production
3. **Use strong SESSION_LIFETIME** values for production
4. **Monitor logs** for any remaining cookie/session issues

## Verification Checklist

- [ ] Environment variables updated in Laravel Cloud dashboard
- [ ] Code deployed with updated CORS configuration
- [ ] Database migrations run successfully
- [ ] Session debug endpoint shows correct configuration
- [ ] `loadAvailability()` function works without errors
- [ ] Browser cookies are set with correct domain
- [ ] No CSRF token mismatches
- [ ] No 500 errors in application logs
