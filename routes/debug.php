<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;

// Debug routes - REMOVE IN PRODUCTION
Route::middleware(['web'])->group(function () {
    
    // Session and Cookie Debug Route
    Route::get('/debug/session-info', function (Request $request) {
        if (app()->environment('production')) {
            abort(404); // Hide in production
        }
        
        return response()->json([
            'session_config' => [
                'driver' => config('session.driver'),
                'lifetime' => config('session.lifetime'),
                'domain' => config('session.domain'),
                'secure' => config('session.secure'),
                'http_only' => config('session.http_only'),
                'same_site' => config('session.same_site'),
                'partitioned' => config('session.partitioned'),
                'cookie_name' => config('session.cookie'),
                'path' => config('session.path'),
            ],
            'app_config' => [
                'app_url' => config('app.url'),
                'app_env' => config('app.env'),
                'app_debug' => config('app.debug'),
            ],
            'request_info' => [
                'host' => $request->getHost(),
                'scheme' => $request->getScheme(),
                'port' => $request->getPort(),
                'full_url' => $request->fullUrl(),
                'user_agent' => $request->userAgent(),
                'ip' => $request->ip(),
                'is_secure' => $request->isSecure(),
            ],
            'session_info' => [
                'session_id' => session()->getId(),
                'session_name' => session()->getName(),
                'csrf_token' => csrf_token(),
                'has_session' => session()->isStarted(),
            ],
            'database_info' => [
                'connection' => config('database.default'),
                'driver' => config('database.connections.' . config('database.default') . '.driver'),
            ],
        ]);
    });
    
    // Test CSRF Token Route
    Route::post('/debug/test-csrf', function (Request $request) {
        if (app()->environment('production')) {
            abort(404); // Hide in production
        }
        
        return response()->json([
            'success' => true,
            'message' => 'CSRF token validation successful',
            'received_data' => $request->all(),
            'session_id' => session()->getId(),
        ]);
    });
    
    // Test Session Storage
    Route::get('/debug/test-session', function (Request $request) {
        if (app()->environment('production')) {
            abort(404); // Hide in production
        }
        
        $testKey = 'debug_test_' . time();
        $testValue = 'test_value_' . rand(1000, 9999);
        
        session()->put($testKey, $testValue);
        $retrievedValue = session()->get($testKey);
        
        return response()->json([
            'session_write_test' => [
                'key' => $testKey,
                'stored_value' => $testValue,
                'retrieved_value' => $retrievedValue,
                'test_passed' => $testValue === $retrievedValue,
            ],
            'session_info' => [
                'session_id' => session()->getId(),
                'session_name' => session()->getName(),
                'is_started' => session()->isStarted(),
            ],
        ]);
    });
});
