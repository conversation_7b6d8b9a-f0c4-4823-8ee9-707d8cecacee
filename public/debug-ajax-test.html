<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AJAX Debug Test - Laravel Cloud</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Laravel Cloud AJAX Debug Test</h1>
    <p><strong>Current URL:</strong> <span id="currentUrl"></span></p>
    
    <div class="test-section">
        <h2>1. Get CSRF Token</h2>
        <button onclick="getCsrfToken()">Get CSRF Token</button>
        <div id="csrfResult"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Test Session Info</h2>
        <button onclick="testSessionInfo()">Test Session Info</button>
        <div id="sessionResult"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Test CORS Info</h2>
        <button onclick="testCorsInfo()">Test CORS Info</button>
        <div id="corsResult"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Test Availability Check (Simulated)</h2>
        <button onclick="testAvailabilityCheck()">Test Availability Check</button>
        <div id="availabilityResult"></div>
    </div>

    <script>
        let csrfToken = '';
        
        document.getElementById('currentUrl').textContent = window.location.href;
        
        function displayResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            const className = isError ? 'error' : 'success';
            element.innerHTML = `<div class="${className}"><pre>${JSON.stringify(data, null, 2)}</pre></div>`;
        }
        
        function displayError(elementId, error) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="error">Error: ${error.message || error}</div>`;
        }
        
        async function getCsrfToken() {
            try {
                const response = await fetch('/debug/session-info');
                const data = await response.json();
                csrfToken = data.session_info.csrf_token;
                displayResult('csrfResult', {
                    csrf_token: csrfToken,
                    session_id: data.session_info.session_id,
                    domain: data.session_config.domain,
                    secure: data.session_config.secure
                });
            } catch (error) {
                displayError('csrfResult', error);
            }
        }
        
        async function testSessionInfo() {
            try {
                const response = await fetch('/debug/session-info');
                const data = await response.json();
                displayResult('sessionResult', data);
            } catch (error) {
                displayError('sessionResult', error);
            }
        }
        
        async function testCorsInfo() {
            try {
                const response = await fetch('/debug/cors-info');
                const data = await response.json();
                displayResult('corsResult', data);
            } catch (error) {
                displayError('corsResult', error);
            }
        }
        
        async function testAvailabilityCheck() {
            if (!csrfToken) {
                displayError('availabilityResult', new Error('Please get CSRF token first'));
                return;
            }
            
            try {
                const response = await fetch('/debug/test-availability', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        field_id: 1,
                        date: '2025-08-09',
                        duration_hours: 2
                    })
                });
                
                const data = await response.json();
                displayResult('availabilityResult', data);
            } catch (error) {
                displayError('availabilityResult', error);
            }
        }
        
        // Auto-get CSRF token on page load
        window.addEventListener('load', getCsrfToken);
    </script>
</body>
</html>
